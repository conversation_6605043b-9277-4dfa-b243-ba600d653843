package com.bxm.customer.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bxm.common.core.constant.CacheConstants;
import com.bxm.common.core.domain.PageResult;
import com.bxm.common.core.domain.Result;
import com.bxm.common.core.enums.BusinessLogBusinessType;
import com.bxm.common.core.enums.DeliverType;
import com.bxm.common.core.enums.DownloadType;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.common.core.utils.poi.ExcelUtil;
import com.bxm.common.core.web.controller.BaseController;
import com.bxm.common.core.web.domain.CommonIdVO;
import com.bxm.common.log.annotation.Log;
import com.bxm.common.log.enums.BusinessType;
import com.bxm.common.redis.service.RedisService;
import com.bxm.common.security.annotation.InnerAuth;
import com.bxm.common.security.utils.SecurityUtils;
import com.bxm.customer.api.domain.dto.*;
import com.bxm.customer.api.domain.vo.*;
import com.bxm.customer.domain.CCustomerService;
import com.bxm.customer.domain.CustomerServicePeriodMonth;
import com.bxm.customer.domain.CustomerServicePeriodYear;
import com.bxm.customer.domain.dto.*;
import com.bxm.customer.domain.dto.newCustomerTransfer.NewCustomerTransferInfoDTO;
import com.bxm.customer.domain.vo.*;
import com.bxm.customer.service.*;
import com.bxm.customer.utils.ExcelUtils;
import com.bxm.system.api.RemoteDeptService;
import com.bxm.system.api.RemoteLogService;
import com.bxm.system.api.domain.BusinessLogDTO;
import com.bxm.system.api.domain.SysDept;
import com.bxm.thirdpart.api.domain.RemoteCompanyInfoDTO;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 客户服务Controller
 *
 * <AUTHOR>
 * @date 2024-05-07
 */
@RestController
@RequestMapping("/customerService")
@Api(tags = "客户服务")
public class CCustomerServiceController extends BaseController {
    @Autowired
    private ICCustomerServiceService cCustomerServiceService;

    @Autowired
    private ICustomerServicePeriodMonthService customerServicePeriodMonthService;

    @Autowired
    private ICustomerServicePeriodYearService customerServicePeriodYearService;

    @Autowired
    private ICustomerServiceInAccountService customerServiceInAccountService;

    @Autowired
    private RemoteLogService remoteLogService;

    @Autowired
    private RemoteDeptService remoteDeptService;

    @Autowired
    private IDownloadRecordService downloadRecordService;

    @Autowired
    private AsyncService asyncService;

    @Autowired
    private ExportService exportService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private RedisService redisService;

//    @RequiresPermissions("customer:customerService:advisorDeptStatistic")
    @GetMapping("/customerServiceAdvisorDeptStatistic")
    @ApiOperation("工作台顾问部门客户数据统计")
    public Result<List<CustomerServiceStatisticDTO>> customerServiceAdvisorDeptStatistic(@RequestHeader("deptId") Long deptId) {
        return Result.ok(cCustomerServiceService.customerServiceAdvisorDeptStatistic(deptId, SecurityUtils.getUserId()));
    }

//    @RequiresPermissions("customer:customerService:accountingDeptStatistic")
    @GetMapping("/customerServiceAccountingDeptStatistic")
    @ApiOperation("工作台会计部门客户数据统计")
    public Result<List<CustomerServiceStatisticDTO>> customerServiceAccountingDeptStatistic(@RequestHeader("deptId") Long deptId) {
        return Result.ok(cCustomerServiceService.customerServiceAccountingDeptStatistic(deptId, SecurityUtils.getUserId()));
    }

//    @RequiresPermissions("customer:customerService:comprehensiveStatistic")
    @GetMapping("/customerServiceComprehensiveStatistic")
    @ApiOperation("工作台综合待办数据统计")
    public Result<CustomerServiceWorkbenchDTO> customerServiceComprehensiveStatistic(@RequestHeader("deptId") Long deptId) {
        return Result.ok(cCustomerServiceService.customerServiceComprehensiveStatistic(deptId, SecurityUtils.getUserId()));
    }

//    @RequiresPermissions("customer:customerService:advisorComprehensiveStatistic")
    @GetMapping("/customerServiceAdvisorComprehensiveStatistic")
    @ApiOperation("工作台顾问综合待办数据统计")
    public Result<CustomerServiceCountDTO> customerServiceAdvisorComprehensiveStatistic(@RequestHeader("deptId") Long headerDeptId, @RequestParam(value = "deptId", required = false) Long deptId) {
        return Result.ok(cCustomerServiceService.customerServiceAdvisorComprehensiveStatistic(deptId, headerDeptId, SecurityUtils.getUserId()));
    }

//    @RequiresPermissions("customer:customerService:accountingComprehensiveStatistic")
    @GetMapping("/customerServiceAccountingComprehensiveStatistic")
    @ApiOperation("工作台会计综合待办数据统计")
    public Result<CustomerServiceCountDTO> customerServiceAccountingComprehensiveStatistic(@RequestHeader("deptId") Long headerDeptId, @RequestParam(value = "deptId", required = false) Long deptId) {
        return Result.ok(cCustomerServiceService.customerServiceAccountingComprehensiveStatistic(deptId, headerDeptId, SecurityUtils.getUserId()));
    }


//    @RequiresPermissions("customer:customerService:advisorComprehensiveStatistic")
    @GetMapping("/customerServiceAdvisorComprehensiveStatisticV2")
    @ApiOperation("工作台顾问综合待办数据统计-V2")
    public Result<CustomerServiceCountV2DTO> customerServiceAdvisorComprehensiveStatisticV2(@RequestHeader("deptId") Long headerDeptId, @RequestParam(value = "deptId", required = false) Long deptId) {
        return Result.ok(cCustomerServiceService.customerServiceAdvisorComprehensiveStatisticV2(deptId, headerDeptId, SecurityUtils.getUserId(), 1));
    }

//    @RequiresPermissions("customer:customerService:accountingComprehensiveStatistic")
    @GetMapping("/customerServiceAccountingComprehensiveStatisticV2")
    @ApiOperation("工作台会计综合待办数据统计-V2")
    public Result<CustomerServiceCountV2DTO> customerServiceAccountingComprehensiveStatisticV2(@RequestHeader("deptId") Long headerDeptId, @RequestParam(value = "deptId", required = false) Long deptId) {
        return Result.ok(cCustomerServiceService.customerServiceAccountingComprehensiveStatisticV2(deptId, headerDeptId, SecurityUtils.getUserId()));
    }


//    @RequiresPermissions(value = {"customer:customerService:medicalInsuranceStatistic", "customer:customerService:socialSecurityStatistic", "customer:customerService:personalTaxStatistic", "customer:customerService:countryTaxStatistic", "customer:customerService:preAuthStatistic"}, logical = Logical.OR)
    @GetMapping("/customerServiceDeliverStatistic")
    @ApiOperation("工作台交付相关数据统计，权限字符（待定）：customer:customerService:medicalInsuranceStatistic（医保统计）,customer:customerService:socialSecurityStatistic（社保统计），customer:customerService:personalTaxStatistic（个税（工资薪金）统计），customer:customerService:countryTaxStatistic（国税统计），customer:customerService:preAuthStatistic（预认证统计）")
    public Result<CustomerServiceDeliverCountDTO> customerServiceDeliverStatistic(@RequestHeader("deptId") Long deptId,
                                                                                  @RequestParam(value = "deptId", required = false) @ApiParam("选择的组织id") Long queryDeptId,
                                                                                  @RequestParam(value = "deptIds", required = false) @ApiParam("选择的组织id") String deptIds,
                                                                                  @RequestParam("deliverType") @ApiParam("交付类型，1-医保，2-社保，3-个税（工资薪金），4-国税，5-预认证，6-个税（经营所得），7-汇算，8-年报，9-残保金，10-次报") Integer deliverType) {
        return Result.ok(cCustomerServiceService.customerServiceDeliverStatistic(deptId, queryDeptId, deptIds, deliverType));
    }

//    @RequiresPermissions(value = {"customer:customerService:medicalInsuranceMiniList", "customer:customerService:medicalInsuranceFrozenMiniList", "customer:customerService:socialSecurityMiniList", "customer:customerService:socialSecurityFrozenMiniList", "customer:customerService:personalTaxMiniList", "customer:customerService:personalTaxFrozenMiniList", "customer:customerService:countryTaxMiniList", "customer:customerService:countryTaxFrozenMiniList", "customer:customerService:preAuthMiniList", "customer:customerService:preAuthFrozenMiniList"}, logical = Logical.OR)
    @GetMapping("/customerDeliverMiniList")
    @ApiOperation("客户交付miniList列表")
    public Result<IPage<CustomerDeliverMiniDTO>> customerDeliverMiniList(@RequestHeader("deptId") Long deptId,
                                                                         @RequestParam(value = "deptId", required = false) @ApiParam("选择的组织id") Long queryDeptId,
                                                                         @RequestParam(value = "deptIds", required = false) @ApiParam("选择的组织id") String deptIds,
                                                                         @RequestParam("deliverType") @ApiParam("交付类型，1-医保，2-社保，3-个税（工资薪金），4-国税，5-预认证，6-个税（经营所得），7-汇算，8-年报，9-残保金，10-次报") Integer deliverType,
                                                                         @RequestParam("deliverStatus") @ApiParam("交付状态，-2-应报户数，-1-待创建，0-待申报，1-申报待提交，2-待确认，3-待扣款，4-扣款待提交，5-待重题，6-申报异常，7-扣款异常，8-冻结待交付，9-待反馈，10-待认证，11-认证异常，13-交付有变更,14-完结待确认，15-完结异常,16-往期未完结,17-往期待交付") Integer deliverStatus,
                                                                         @RequestParam(value = "status", required = false) @ApiParam("交付状态，0-已提交待申报，1，申报已保存待提交，2-已申报待确认，3-已确认待扣款，4-扣款已保存待提交，5-已扣款，6-待重提，7-申报异常，8-暂不申报，9-扣款异常，10-暂不扣款，21-待提交") Integer status,
                                                                         @RequestParam(value = "keyWord", required = false) @ApiParam("客户名称") String customerName,
                                                                         @RequestParam(value = "tagName", required = false) @ApiParam("标签名称") String tagName,
                                                                         @RequestParam(value = "tagIncludeFlag", required = false) @ApiParam("是否包含标签，0-否，1-是") Integer tagIncludeType,
                                                                         @RequestParam(value = "advisorDeptId", required = false) @ApiParam("顾问部门id") Long advisorDeptId,
                                                                         @RequestParam(value = "accountingDeptId", required = false) @ApiParam("会计部门id") Long accountingDeptId,
                                                                         @RequestParam(value = "customerServiceTagName", required = false) @ApiParam("服务标签名称") String customerServiceTagName,
                                                                         @RequestParam(value = "customerServiceTagIncludeFlag", required = false) @ApiParam("是否包含服务标签，0-否，1-是") Integer customerServiceTagIncludeFlag,
                                                                         @RequestParam(value = "customerServiceAdvisorDeptId", required = false) @ApiParam("服务顾问部门id") Long customerServiceAdvisorDeptId,
                                                                         @RequestParam(value = "customerServiceAccountingDeptId", required = false) @ApiParam("服务会计部门id") Long customerServiceAccountingDeptId,
                                                                         @RequestParam(value = "customerServiceTaxType", required = false) @ApiParam("服务纳税人性质") Integer customerServiceTaxType,
                                                                         @RequestParam(value = "periodTagName", required = false) @ApiParam("账期标签名称") String periodTagName,
                                                                         @RequestParam(value = "periodTagIncludeFlag", required = false) @ApiParam("是否包含账期标签，0-否，1-是") Integer periodTagIncludeFlag,
                                                                         @RequestParam(value = "periodAdvisorDeptId", required = false) @ApiParam("账期顾问部门id") Long periodAdvisorDeptId,
                                                                         @RequestParam(value = "periodAccountingDeptId", required = false) @ApiParam("账期会计部门id") Long periodAccountingDeptId,
                                                                         @RequestParam(value = "periodTaxType", required = false) @ApiParam("账期纳税人性质") Integer periodTaxType,
                                                                         @RequestParam(value = "taxCheckType", required = false) @ApiParam("申报税种") String taxCheckType,
                                                                         @RequestParam(value = "pageNum", required = false, defaultValue = "1") @ApiParam("页码") Integer pageNum,
                                                                         @RequestParam(value = "pageSize", required = false, defaultValue = "10") @ApiParam("每页条数") Integer pageSize) {
        return Result.ok(cCustomerServiceService.customerDeliverMiniList(deptId, queryDeptId, deptIds, deliverType, deliverStatus, customerName, tagName, tagIncludeType, null, advisorDeptId, accountingDeptId, status, customerServiceTagName, customerServiceTagIncludeFlag, customerServiceAdvisorDeptId, customerServiceAccountingDeptId, customerServiceTaxType, periodTagName, periodTagIncludeFlag, periodAdvisorDeptId, periodAccountingDeptId, periodTaxType, taxCheckType, pageNum, pageSize));
    }

    private Integer getDefaultPeriodByDeliverType(Integer deliverType) {
        Integer finalPeriod;
        if (deliverType == 3 || deliverType == 4 || deliverType == 6) {
            finalPeriod = DateUtils.getPrePeriod();
        } else if (deliverType == 7 || deliverType == 8 || deliverType == 9) {
            finalPeriod = Integer.parseInt(LocalDateTime.now().getYear() - 1 + "12");
        } else if (deliverType == 10) {
            finalPeriod = null;
        } else {
            finalPeriod = DateUtils.getNowPeriod();
        }
        return finalPeriod;
    }

//    @RequiresPermissions(value = {"customer:customerService:medicalInsuranceMiniExport", "customer:customerService:socialSecurityMiniExport", "customer:customerService:personalTaxMiniExport", "customer:customerService:countryTaxMiniExport", "customer:customerService:preAuthMiniExport"}, logical = Logical.OR)
    @PostMapping("/customerDeliverMiniExport")
    @ApiOperation("客户交付miniList导出")
    public void customerDeliverMiniExport(@RequestHeader("deptId") Long deptId,
                                          @RequestParam(value = "deptId", required = false) @ApiParam("选择的组织id") Long queryDeptId,
                                          @RequestParam(value = "deptIds", required = false) @ApiParam("选择的组织id") String deptIds,
                                          @RequestParam("deliverType") @ApiParam("交付类型，1-医保，2-社保，3-个税（工资薪金），4-国税，5-预认证，6-个税（经营所得），7-汇算，8-年报，9-残保金") Integer deliverType,
                                          @RequestParam("deliverStatus") @ApiParam("交付状态，-2-应报户数，-1-待创建，0-待申报，1-申报待提交，2-待确认，3-待扣款，4-扣款待提交，5-待重题，6-申报异常，7-扣款异常，8-冻结待交付，9-待反馈，10-待认证，11-认证异常，13-交付有变更,14-完结待确认，15-完结异常,16-往期未完结,17-往期待交付") Integer deliverStatus,
                                          @RequestParam(value = "status", required = false) @ApiParam("交付状态，0-已提交待申报，1，申报已保存待提交，2-已申报待确认，3-已确认待扣款，4-扣款已保存待提交，5-已扣款，6-待重提，7-申报异常，8-暂不申报，9-扣款异常，10-暂不扣款") Integer status,
                                          @RequestParam(value = "keyWord", required = false) @ApiParam("客户名称") String customerName,
                                          @RequestParam(value = "tagName", required = false) @ApiParam("标签名称") String tagName,
                                          @RequestParam(value = "tagIncludeFlag", required = false) @ApiParam("是否包含标签，0-否，1-是") Integer tagIncludeType,
                                          @RequestParam(value = "advisorDeptId", required = false) @ApiParam("顾问部门id") Long advisorDeptId,
                                          @RequestParam(value = "accountingDeptId", required = false) @ApiParam("会计部门id") Long accountingDeptId,
                                          @RequestParam(value = "customerServiceTagName", required = false) @ApiParam("服务标签名称") String customerServiceTagName,
                                          @RequestParam(value = "customerServiceTagIncludeFlag", required = false) @ApiParam("是否包含服务标签，0-否，1-是") Integer customerServiceTagIncludeFlag,
                                          @RequestParam(value = "customerServiceAdvisorDeptId", required = false) @ApiParam("服务顾问部门id") Long customerServiceAdvisorDeptId,
                                          @RequestParam(value = "customerServiceAccountingDeptId", required = false) @ApiParam("服务会计部门id") Long customerServiceAccountingDeptId,
                                          @RequestParam(value = "customerServiceTaxType", required = false) @ApiParam("服务纳税人性质") Integer customerServiceTaxType,
                                          @RequestParam(value = "periodTagName", required = false) @ApiParam("账期标签名称") String periodTagName,
                                          @RequestParam(value = "periodTagIncludeFlag", required = false) @ApiParam("是否包含账期标签，0-否，1-是") Integer periodTagIncludeFlag,
                                          @RequestParam(value = "periodAdvisorDeptId", required = false) @ApiParam("账期顾问部门id") Long periodAdvisorDeptId,
                                          @RequestParam(value = "periodAccountingDeptId", required = false) @ApiParam("账期会计部门id") Long periodAccountingDeptId,
                                          @RequestParam(value = "periodTaxType", required = false) @ApiParam("账期纳税人性质") Integer periodTaxType,
                                          @RequestParam(value = "taxCheckType", required = false) @ApiParam("申报税种") String taxCheckType,
                                          HttpServletResponse response) {
        Integer period = getDefaultPeriodByDeliverType(deliverType);
        String deliverStatusStr = "";
        switch (deliverStatus) {
            case -2:
                deliverStatusStr = "应报户数";
                break;
            case -1:
                deliverStatusStr = "待创建";
                break;
            case 0:
                deliverStatusStr = "待申报";
                break;
            case 1:
                deliverStatusStr = "申报待提交";
                break;
            case 2:
                deliverStatusStr = "待确认";
                break;
            case 3:
                deliverStatusStr = "待扣款";
                break;
            case 4:
                deliverStatusStr = "扣款待提交";
                break;
            case 5:
                deliverStatusStr = "待提交";
                break;
            case 6:
                deliverStatusStr = "申报异常";
                break;
            case 7:
                deliverStatusStr = "扣款异常";
                break;
            case 8:
                deliverStatusStr = "冻结待交付";
                break;
            case 9:
                deliverStatusStr = "待反馈";
                break;
            case 10:
                deliverStatusStr = "待认证";
                break;
            case 11:
                deliverStatusStr = "认证异常";
                break;
            case 13:
                deliverStatusStr = "交付有变更";
                break;
            case 14:
                deliverStatusStr = "完结待确认";
                break;
            case 15:
                deliverStatusStr = "完结异常";
                break;
            case 16:
                deliverStatusStr = "往期未完结";
                break;
            case 17:
                deliverStatusStr = "往期待交付";
                break;
            default:
                deliverStatusStr = "";
                break;
        }
        Integer pageNum = 1;
        Integer pageSize = 5000;
        List<CustomerDeliverMiniDTO> list = Lists.newArrayList();
        while (true) {
            List<CustomerDeliverMiniDTO> records = cCustomerServiceService.customerDeliverMiniList(deptId, queryDeptId, deptIds, deliverType, deliverStatus, customerName, tagName, tagIncludeType, null, advisorDeptId, accountingDeptId, status, customerServiceTagName, customerServiceTagIncludeFlag, customerServiceAdvisorDeptId, customerServiceAccountingDeptId, customerServiceTaxType, periodTagName, periodTagIncludeFlag, periodAdvisorDeptId, periodAccountingDeptId, periodTaxType, taxCheckType, pageNum, pageSize).getRecords();
            if (!ObjectUtils.isEmpty(records)) {
                list.addAll(records);
                pageNum++;
            } else {
                break;
            }
        }
        ExcelUtil<CustomerDeliverMiniDTO> util = new ExcelUtil<>(CustomerDeliverMiniDTO.class);
//        if (Lists.newArrayList(DeliverType.ANNUAL_REPORT.getCode(), DeliverType.SETTLE_ACCOUNTS.getCode()).contains(deliverType)) {
//            util.hideColumn("periodStr");
//        }
        util.exportExcel(response, list, "【" + period + "】" + DeliverType.getByCode(deliverType).getName() + deliverStatusStr + "列表");
    }

    /**
     * 查询客户服务列表
     */
//    @RequiresPermissions("customer:customerService:list")
    @GetMapping("/list")
    @ApiOperation(value = "查询客户服务列表", notes = "查询客户服务列表")
    public Result<IPage<CustomerServiceDTO>> list(@RequestHeader("deptId") Long deptId,
                                                  CustomerServiceSearchVO vo) {
        return Result.ok(cCustomerServiceService.customerServiceList(deptId, vo));
    }

    @GetMapping("/sameCreditCodeServiceList")
    @ApiOperation(value = "查询同税号客户服务列表", notes = "查询同税号客户服务列表")
    public Result<List<CustomerServiceDTO>> sameCreditCodeServiceList(@RequestParam("customerServiceId") Long customerServiceId) {
        return Result.ok(cCustomerServiceService.sameCreditCodeServiceList(customerServiceId));
    }

    /**
     * 客户管理 -> 月度汇总
     * mmnAPI
     */
//    @RequiresPermissions("customer:customerService:list")
    @GetMapping("/periodMonthList")
    @ApiOperation(value = "客户管理 -> 月度汇总", notes = "客户管理 -> 月度汇总")
//    @RequiresPermissions(value = {"customer:customerService:periodMonthList", "customer:customerService:thisPeriodMonthList", "customer:customerService:prePeriodMonthList"}, logical = Logical.OR)
    public Result<IPage<CustomerServicePeriodMonthDTO>> customerServicePeriodMonthList(@RequestHeader("deptId") Long deptId, CustomerServicePeriodMonthSearchVO vo) {
        return Result.ok(cCustomerServiceService.customerServicePeriodMonthList(deptId, vo));
    }

    /**
     * 导出客户服务列表
     */
//    @RequiresPermissions("customer:customerService:exportCustomerServicePeriodMonthList")
    @Log(title = "客户服务", businessType = BusinessType.EXPORT)
    @PostMapping("/exportCustomerServicePeriodMonthList")
    @ApiOperation(value = "客户管理 -> 月度汇总 -> 导出月度汇总列表", notes = "导出月度汇总列表")
    public void exportCustomerServicePeriodMonthList(HttpServletResponse response, @RequestHeader("deptId") Long deptId, CustomerServicePeriodMonthSearchVO vo) {
        vo.setPageNum(1);
        vo.setPageSize(30000);
        List<CustomerServicePeriodMonthDTO> list = cCustomerServiceService.customerServicePeriodMonthList(deptId, vo).getRecords();
        ExcelUtil<CustomerServicePeriodMonthDTO> util = new ExcelUtil<>(CustomerServicePeriodMonthDTO.class);
        util.exportExcel(response, list, "月度汇总数据");
    }

    /**
     * 导出账期列表(上传oss)
     */
//    @RequiresPermissions("customer:customerService:exportCustomerServicePeriodMonthList")
    @Log(title = "客户服务", businessType = BusinessType.EXPORT)
    @PostMapping("/exportCustomerServicePeriodMonthListAndUpload")
    @ApiOperation(value = "导出账期列表(上传oss)", notes = "导出账期列表(上传oss)")
    public Result exportCustomerServicePeriodMonthListAndUpload(@RequestHeader("deptId") Long deptId, CustomerServicePeriodMonthSearchVO vo) {
        String title = "服务-账期列表" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        vo.setDeptId(deptId);
        vo.setUserId(SecurityUtils.getUserId());
//        Long downloadRecordId = downloadRecordService.createRecord(title, 0L, 0L, vo, DownloadType.SERVICE_BILL_PERIOD);
//        CompletableFuture.runAsync(() -> {
//            try {
//                List<CustomerServicePeriodMonthDTO> list = Lists.newArrayList();
//                Integer pageNum = 1;
//                Integer pageSize = 5000;
//                vo.setPageSize(pageSize);
//                while (true) {
//                    vo.setPageNum(pageNum);
//                    List<CustomerServicePeriodMonthDTO> l = cCustomerServiceService.customerServicePeriodMonthList(deptId, vo).getRecords();
//                    if (!ObjectUtils.isEmpty(l)) {
//                        list.addAll(l);
//                        pageNum++;
//                    } else {
//                        break;
//                    }
//                }
//                downloadRecordService.updateDataCount(downloadRecordId, (long) list.size());
//                ExcelUtil<CustomerServicePeriodMonthDTO> util = new ExcelUtil<>(CustomerServicePeriodMonthDTO.class);
//                asyncService.uploadExport(util, list, title, downloadRecordId);
//            } catch (Exception e) {
//                downloadRecordService.updateDownloadError(downloadRecordId, e.getMessage());
//            }
//        });

        exportService.exportAsync(
                title,
                vo,
                deptId,
                cCustomerServiceService::customerServicePeriodMonthList,
                DownloadType.SERVICE_BILL_PERIOD,
                CustomerServicePeriodMonthDTO.class,
                downloadRecordService
        );
        return Result.ok();
    }

    @PostMapping("/v2/exportCustomerServicePeriodMonthListAndUpload")
    @ApiOperation(value = "导出客户账期列表，支持银行账号，系统账号，客户信息")
    public Result exportCustomerServicePeriodMonthListAndUploadV2(@RequestHeader("deptId") Long deptId, CustomerServicePeriodMonthSearchVO vo) {
        if (StringUtils.isEmpty(vo.getExportTypes())) {
            return Result.fail("导出类型不能为空");
        }
        SysDept sysDept = remoteDeptService.getDeptInfo(deptId).getDataThrowException();
        if (Objects.isNull(sysDept)) {
            return Result.fail("当前公司有误");
        }
        String title = "服务-账期列表" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        vo.setDeptId(deptId);
        vo.setUserId(SecurityUtils.getUserId());
        Long downloadRecordId = downloadRecordService.createRecord(title, 0L, 0L, vo, DownloadType.SERVICE_BILL_PERIOD);
        CompletableFuture.runAsync(() -> {
            try {
                List<CustomerServicePeriodMonthDTO> list = Lists.newArrayList();
                Integer pageNum = 1;
                Integer pageSize = 1000;
                vo.setPageSize(pageSize);
                while (true) {
                    vo.setPageNum(pageNum);
                    List<CustomerServicePeriodMonthDTO> l = cCustomerServiceService.customerServicePeriodMonthList(deptId, vo).getRecords();
                    if (!ObjectUtils.isEmpty(l)) {
                        list.addAll(l);
                        pageNum++;
                    } else {
                        break;
                    }
                }
                downloadRecordService.updateDataCount(downloadRecordId, (long) list.size());

                File zipFile = File.createTempFile("temp", ".zip");
                try (ZipOutputStream zos = new ZipOutputStream(new FileOutputStream(zipFile))) {
                    if (vo.getExportTypes().contains("1")) {
                        Map<String, Class<?>> sheetClassMap = new HashMap<>();
                        Map<String, List<?>> dataMap = new HashMap<>();
                        sheetClassMap.put("客户信息", CustomerServicePeriodMonthDTO.class);
                        dataMap.put("客户信息", list);
                        Workbook workbook = ExcelUtils.exportExcelZip(dataMap, sheetClassMap);
                        ZipEntry excelEntry = new ZipEntry("客户信息.xlsx");
                        zos.putNextEntry(excelEntry);
                        try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
                            workbook.write(baos);
                            zos.write(baos.toByteArray());
                        }
                    }
                    if (vo.getExportTypes().contains("2")) {
                        Map<String, Class<?>> sheetClassMap = new HashMap<>();
                        Map<String, List<?>> dataMap = new HashMap<>();
                        if (Objects.equals(sysDept.getDeptType(), 1)) {
                            sheetClassMap.put("银行账号", CustomerServiceBankBusinessDTO.class);
                            dataMap.put("银行账号", list.stream().map(CustomerServicePeriodMonthDTO::getBankBusinessList).flatMap(List::stream).collect(Collectors.toList()));
                        } else {
                            sheetClassMap.put("银行账号", CustomerServiceBankDTO.class);
                            dataMap.put("银行账号", list.stream().map(CustomerServicePeriodMonthDTO::getBankList).flatMap(List::stream).collect(Collectors.toList()));
                        }
                        Workbook workbook = ExcelUtils.exportExcelZip(dataMap, sheetClassMap);
                        ZipEntry putNextEntry = new ZipEntry("银行账号.xlsx");
                        zos.putNextEntry(putNextEntry);
                        try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
                            workbook.write(baos);
                            zos.write(baos.toByteArray());
                        }
                    }
                    if (vo.getExportTypes().contains("3")) {
                        Map<String, Class<?>> sheetClassMap = new HashMap<>();
                        Map<String, List<?>> dataMap = new HashMap<>();
                        if (Objects.equals(sysDept.getDeptType(), 1)) {
                            sheetClassMap.put("税种", CustomerPeriodTaxTypeCheckBusinessDTO.class);
                            dataMap.put("税种", list.stream().map(CustomerServicePeriodMonthDTO::getTaxTypeCheckBusinessList).flatMap(List::stream).collect(Collectors.toList()));
                        } else {
                            sheetClassMap.put("税种", CustomerPeriodTaxTypeCheckDTO.class);
                            dataMap.put("税种", list.stream().map(CustomerServicePeriodMonthDTO::getTaxTypeCheckList).flatMap(List::stream).collect(Collectors.toList()));
                        }
                        Workbook workbook = ExcelUtils.exportExcelZip(dataMap, sheetClassMap);
                        ZipEntry putNextEntry = new ZipEntry("税种.xlsx");
                        zos.putNextEntry(putNextEntry);
                        try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
                            workbook.write(baos);
                            zos.write(baos.toByteArray());
                        }
                    }
                    if (vo.getExportTypes().contains("4")) {
                        Map<String, Class<?>> sheetClassMap = new HashMap<>();
                        Map<String, List<?>> dataMap = new HashMap<>();
                        if (Objects.equals(sysDept.getDeptType(), 1)) {
                            sheetClassMap.put("系统账号", CustomerSysAccountBusinessDTO.class);
                            dataMap.put("系统账号", list.stream().map(CustomerServicePeriodMonthDTO::getSysAccountBusinessList).flatMap(List::stream).collect(Collectors.toList()));
                        } else {
                            sheetClassMap.put("系统账号", CustomerSysAccountDTO.class);
                            dataMap.put("系统账号", list.stream().map(CustomerServicePeriodMonthDTO::getSysAccountList).flatMap(List::stream).collect(Collectors.toList()));
                        }
                        Workbook workbook = ExcelUtils.exportExcelZip(dataMap, sheetClassMap);
                        ZipEntry putNextEntry = new ZipEntry("系统账号.xlsx");
                        zos.putNextEntry(putNextEntry);
                        try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
                            workbook.write(baos);
                            zos.write(baos.toByteArray());
                        }
                    }
                }
                asyncService.uploadExport(zipFile, (long) list.size(), downloadRecordId);
            } catch (Exception e) {
                downloadRecordService.updateDownloadError(downloadRecordId, e.getMessage());
            }
        });

        return Result.ok();
    }

    /**
     * 客户管理 -> 账期详情 -> 编辑/账期服务信息修改
     * mmnAPI
     */
//    @RequiresPermissions("customer:customerServicePeriodMonth:edit")
    @Log(title = "客户服务", businessType = BusinessType.UPDATE)
    @PostMapping("/updatePeriodMonthServiceInfo")
    @ApiOperation(value = "客户管理 -> 账期详情 -> 编辑/账期服务信息修改", notes = "信息变更")
    public Result<Boolean> updatePeriodMonthServiceInfo(@RequestHeader("deptId") Long deptId, @RequestBody @Valid UpdatePeriodMonthDTO dto) {
        return Result.ok(cCustomerServiceService.updateCustomerServicePeriodMonthServiceInfo(deptId, dto));
    }

    /**
     * 导出客户服务列表
     */
//    @RequiresPermissions("customer:customerService:export")
    @Log(title = "客户服务", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation(value = "导出客户服务列表", notes = "导出客户服务列表")
    public void export(HttpServletResponse response, @RequestHeader("deptId") Long deptId, CustomerServiceSearchVO vo) {
        vo.setPageNum(1);
        vo.setPageSize(-1);
        List<CustomerServiceDTO> list = cCustomerServiceService.customerServiceList(deptId, vo).getRecords();
        ExcelUtil<CustomerServiceDTO> util = new ExcelUtil<>(CustomerServiceDTO.class);
//        if (条件A) {
//            // 不显示用户ID（单个）
//            util.hideColumn("userId");
//        } else if (条件B) {
//            // 不显示用户名称、用户手机（多个）
//            util.hideColumn("userId", "phonenumber");
//        } } else if (条件C) {
        // 不显示用户邮箱、部门名称（子对象）
//        util.hideColumn("email", "dept.deptName");
        util.exportExcel(response, list, "客户服务数据");
    }

    /**
     * 导出客户服务列表(上传oss)
     */
//    @RequiresPermissions("customer:customerService:export")
    @Log(title = "客户服务", businessType = BusinessType.EXPORT)
    @PostMapping("/v2/exportAndUpload")
    @ApiOperation(value = "导出客户服务列表(上传oss)", notes = "导出客户服务列表(上传oss)")
    public Result exportAndUpload(@RequestHeader("deptId") Long deptId, CustomerServiceSearchVO vo) {
        String title = "服务-客户列表" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        vo.setUserId(SecurityUtils.getUserId());
        vo.setDeptId(deptId);
        Long downloadRecordId = downloadRecordService.createRecord(title, 0L, 0L, vo, DownloadType.SERVICE_CUSTOMER_LIST);
        CompletableFuture.runAsync(() -> {
            try {
                List<CustomerServiceDTO> list = Lists.newArrayList();
                Integer pageNum = 1;
                Integer pageSize = 5000;
                vo.setPageSize(pageSize);
                while (true) {
                    vo.setPageNum(pageNum);
                    List<CustomerServiceDTO> l = cCustomerServiceService.customerServiceList(deptId, vo).getRecords();
                    if (!ObjectUtils.isEmpty(l)) {
                        list.addAll(l);
                        pageNum++;
                    } else {
                        break;
                    }
                }
                downloadRecordService.updateDataCount(downloadRecordId, (long) list.size());
                ExcelUtil<CustomerServiceDTO> util = new ExcelUtil<>(CustomerServiceDTO.class);
                asyncService.uploadExport(util, list, title, downloadRecordId);
            } catch (Exception e) {
                downloadRecordService.updateDownloadError(downloadRecordId, e.getMessage());
            }
        });

        return Result.ok();
    }

    @PostMapping("/exportAndUpload")
    @ApiOperation(value = "导出客户服务列表(上传oss)", notes = "导出客户服务列表(上传oss)")
    public Result exportAndUploadV2(@RequestHeader("deptId") Long deptId, CustomerServiceSearchVO vo) {
        if (StringUtils.isEmpty(vo.getExportTypes())) {
            return Result.fail("导出类型不能为空");
        }
        SysDept sysDept = remoteDeptService.getDeptInfo(deptId).getDataThrowException();
        if (Objects.isNull(sysDept)) {
            return Result.fail("当前公司有误");
        }
        String title = "服务-客户列表" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        vo.setDeptId(deptId);
        vo.setUserId(SecurityUtils.getUserId());
        Long downloadRecordId = downloadRecordService.createRecord(title, 0L, 0L, vo, DownloadType.SERVICE_CUSTOMER_LIST);
        CompletableFuture.runAsync(() -> {
            try {
                List<CustomerServiceDTO> list = Lists.newArrayList();
                Integer pageNum = 1;
                Integer pageSize = 5000;
                vo.setPageSize(pageSize);
                while (true) {
                    vo.setPageNum(pageNum);
                    List<CustomerServiceDTO> l = cCustomerServiceService.customerServiceList(deptId, vo).getRecords();
                    if (!ObjectUtils.isEmpty(l)) {
                        list.addAll(l);
                        pageNum++;
                    } else {
                        break;
                    }
                }
                downloadRecordService.updateDataCount(downloadRecordId, (long) list.size());

                File zipFile = File.createTempFile("temp", ".zip");
                try (ZipOutputStream zos = new ZipOutputStream(new FileOutputStream(zipFile))) {
                    if (vo.getExportTypes().contains("1")) {
                        Map<String, Class<?>> sheetClassMap = new HashMap<>();
                        Map<String, List<?>> dataMap = new HashMap<>();
                        sheetClassMap.put("客户信息", CustomerServiceDTO.class);
                        dataMap.put("客户信息", list);
                        Workbook workbook = ExcelUtils.exportExcelZip(dataMap, sheetClassMap);
                        ZipEntry excelEntry = new ZipEntry("客户信息.xlsx");
                        zos.putNextEntry(excelEntry);
                        try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
                            workbook.write(baos);
                            zos.write(baos.toByteArray());
                        }
                    }
                    if (vo.getExportTypes().contains("2")) {
                        Map<String, Class<?>> sheetClassMap = new HashMap<>();
                        Map<String, List<?>> dataMap = new HashMap<>();
                        if (Objects.equals(sysDept.getDeptType(), 1)) {
                            sheetClassMap.put("银行账号", CustomerServiceBankBusinessDTO.class);
                            dataMap.put("银行账号", list.stream().map(CustomerServiceDTO::getBankBusinessList).flatMap(List::stream).collect(Collectors.toList()));
                        } else {
                            sheetClassMap.put("银行账号", CustomerServiceBankDTO.class);
                            dataMap.put("银行账号", list.stream().map(CustomerServiceDTO::getBankList).flatMap(List::stream).collect(Collectors.toList()));
                        }
                        Workbook workbook = ExcelUtils.exportExcelZip(dataMap, sheetClassMap);
                        ZipEntry putNextEntry = new ZipEntry("银行账号.xlsx");
                        zos.putNextEntry(putNextEntry);
                        try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
                            workbook.write(baos);
                            zos.write(baos.toByteArray());
                        }
                    }
                    if (vo.getExportTypes().contains("3")) {
                        Map<String, Class<?>> sheetClassMap = new HashMap<>();
                        Map<String, List<?>> dataMap = new HashMap<>();
                        if (Objects.equals(sysDept.getDeptType(), 1)) {
                            sheetClassMap.put("税种", CustomerTaxTypeCheckBusinessDTO.class);
                            dataMap.put("税种", list.stream().map(CustomerServiceDTO::getTaxTypeCheckBusinessList).flatMap(List::stream).collect(Collectors.toList()));
                        } else {
                            sheetClassMap.put("税种", CustomerTaxTypeCheckDTO.class);
                            dataMap.put("税种", list.stream().map(CustomerServiceDTO::getTaxTypeCheckList).flatMap(List::stream).collect(Collectors.toList()));
                        }
                        Workbook workbook = ExcelUtils.exportExcelZip(dataMap, sheetClassMap);
                        ZipEntry putNextEntry = new ZipEntry("税种.xlsx");
                        zos.putNextEntry(putNextEntry);
                        try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
                            workbook.write(baos);
                            zos.write(baos.toByteArray());
                        }
                    }
                    if (vo.getExportTypes().contains("4")) {
                        Map<String, Class<?>> sheetClassMap = new HashMap<>();
                        Map<String, List<?>> dataMap = new HashMap<>();
                        if (Objects.equals(sysDept.getDeptType(), 1)) {
                            sheetClassMap.put("系统账号", CustomerSysAccountBusinessDTO.class);
                            dataMap.put("系统账号", list.stream().map(CustomerServiceDTO::getSysAccountBusinessList).flatMap(List::stream).collect(Collectors.toList()));
                        } else {
                            sheetClassMap.put("系统账号", CustomerSysAccountDTO.class);
                            dataMap.put("系统账号", list.stream().map(CustomerServiceDTO::getSysAccountList).flatMap(List::stream).collect(Collectors.toList()));
                        }
                        Workbook workbook = ExcelUtils.exportExcelZip(dataMap, sheetClassMap);
                        ZipEntry putNextEntry = new ZipEntry("系统账号.xlsx");
                        zos.putNextEntry(putNextEntry);
                        try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
                            workbook.write(baos);
                            zos.write(baos.toByteArray());
                        }
                    }
                }
                asyncService.uploadExport(zipFile, (long) list.size(), downloadRecordId);
            } catch (Exception e) {
                downloadRecordService.updateDownloadError(downloadRecordId, e.getMessage());
            }
        });

        return Result.ok();
    }

    /**
     * 获取客户服务详细信息
     */
//    @RequiresPermissions("customer:customerService:query")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取客户服务详细信息", notes = "获取客户服务详细信息")
    public Result<CustomerServiceDetailDTO> getInfo(@PathVariable("id") Long id, @RequestHeader("deptId") Long deptId) {
        return Result.ok(cCustomerServiceService.customerServiceDetail(id, deptId));
    }

    @GetMapping(value = "/detailNoDataScope/{id}")
    @ApiOperation(value = "获取客户服务详细信息", notes = "获取客户服务详细信息")
    public Result<CustomerServiceDetailDTO> getInfoNoDataScope(@PathVariable("id") Long id, @RequestHeader("deptId") Long deptId) {
        return Result.ok(cCustomerServiceService.customerServiceDetailNoDataScope(id, deptId));
    }

    @GetMapping(value = "/getNewCustomerTransferInfo/{id}")
    @ApiOperation(value = "获取新户流转相关信息，如果data=null，代表不显示该模块")
    public Result<NewCustomerTransferInfoDTO> getNewCustomerTransferInfo(@PathVariable("id") @ApiParam("服务id") Long id) {
        return Result.ok(cCustomerServiceService.getNewCustomerTransferInfo(id));
    }

    /**
     * 新增客户服务
     */
//    @RequiresPermissions("customer:customerService:add")
    @Log(title = "客户服务", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ApiOperation(value = "新增客户服务", notes = "新增客户服务")
    public Result add(@RequestBody @Valid CustomerServiceDetailDTO dto,
                      @RequestHeader("deptId") Long deptId) {
        cCustomerServiceService.addCustomerService(dto, deptId);
        return Result.ok();
    }

    /**
     * 修改客户服务
     */
//    @RequiresPermissions("customer:customerService:edit")
    @Log(title = "客户服务", businessType = BusinessType.UPDATE)
    @PostMapping("/update")
    @ApiOperation(value = "信息变更", notes = "信息变更")
    public Result edit(@RequestBody @Valid CustomerServiceDetailDTO dto,
                       @RequestHeader("deptId") Long deptId) {
        cCustomerServiceService.updateCustomerService(dto, deptId);
        return Result.ok();
    }

    /**
     * 删除客户服务
     */
//    @RequiresPermissions("customer:customerService:remove")
    @Log(title = "客户服务", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation(value = "删除客户服务", notes = "删除客户服务")
    public Result<CommonOperateDTO> remove(@PathVariable Long[] ids) {
        return Result.ok(cCustomerServiceService.deleteCCustomerServiceByIds(ids));
    }


    @GetMapping(value = "/preEndServiceInfo")
    @ApiOperation(value = "获取 结束服务/移除 操作时的前置数据", notes = "获取 结束服务/移除 操作时的前置数据")
    public Result<PreEndServiceInfoDTO> preEndServiceInfo(@RequestParam("id") Long id) {
        return Result.ok(cCustomerServiceService.preEndServiceInfo(id));
    }

    /**
     * 结束客户服务
     */
//    @RequiresPermissions("customer:customerService:end")
    @Log(title = "客户服务", businessType = BusinessType.END)
    @PostMapping("/endService")
    @ApiOperation(value = "结束客户服务", notes = "结束客户服务")
    public Result<CommonOperateDTO> endService(@RequestBody CustomerServiceEndVO vo) {
        return Result.ok(cCustomerServiceService.endService(vo));
    }

    /**
     * 结束客户服务-V2
     */
//    @RequiresPermissions("customer:customerService:end")
    @Log(title = "客户服务", businessType = BusinessType.END)
    @PostMapping("/endServiceV2")
    @ApiOperation(value = "结束客户服务-V2", notes = "结束客户服务-V2")
    public Result<CommonOperateDTO> endServiceV2(@RequestBody CustomerServiceEndV2VO vo) {
        return Result.ok(cCustomerServiceService.endServiceV2(vo));
    }

    /**
     * 重启客户服务
     */
//    @RequiresPermissions("customer:customerService:restart")
    @Log(title = "客户服务", businessType = BusinessType.RESTART)
    @PostMapping("/restartService")
    @ApiOperation(value = "重启客户服务", notes = "重启客户服务")
    public Result<CommonOperateDTO> restartService(@RequestBody CustomerServiceRestartVO vo) {
        return Result.ok(cCustomerServiceService.restartService(vo));
    }

    /**
     * 重启客户服务-V2
     */
//    @RequiresPermissions("customer:customerService:restart")
    @Log(title = "客户服务", businessType = BusinessType.RESTART)
    @PostMapping("/restartServiceV2")
    @ApiOperation(value = "重启客户服务-V2", notes = "重启客户服务-V2")
    public Result<CommonOperateDTO> restartServiceV2(@RequestBody CustomerServiceRestartVO vo) {
        return Result.ok(cCustomerServiceService.restartServiceV2(vo));
    }

    @GetMapping(value = "/customerServiceRestartCheck")
    @ApiOperation(value = "检查重启是否会补账")
    public Result<CustomerServiceRestartCheckDTO> customerServiceRestartCheck(@RequestParam("customerServiceId") @ApiParam("客户服务id") Long customerServiceId) {
        return Result.ok(cCustomerServiceService.customerServiceRestartCheck(customerServiceId));
    }

    /**
     * 冻结客户服务
     */
//    @RequiresPermissions("customer:customerService:froze")
    @Log(title = "客户服务", businessType = BusinessType.FROZE)
    @PostMapping("/frozeService")
    @ApiOperation(value = "冻结客户服务", notes = "冻结客户服务")
    public Result<CommonOperateDTO> frozeService(@RequestBody CustomerServiceFrozeVO vo) {
        return Result.ok(cCustomerServiceService.frozeService(vo));
    }

    @GetMapping(value = "/getUnFrozeServicePeriod")
    @ApiOperation(value = "获取解冻账期范围", notes = "获取解冻账期范围")
    public Result<UnFrozeServicePeriodDTO> getUnFrozeServicePeriod(@RequestParam("id") Long id) {
        return Result.ok(cCustomerServiceService.getUnFrozeServicePeriod(id));
    }

    /**
     * 解冻客户服务
     */
//    @RequiresPermissions("customer:customerService:unFroze")
    @Log(title = "客户服务", businessType = BusinessType.UNFROZE)
    @PostMapping("/unFrozeService")
    @ApiOperation(value = "解冻客户服务", notes = "解冻客户服务")
    public Result<CommonOperateDTO> unFrozeService(@RequestBody CustomerServiceUnFrozeVO vo) {
        return Result.ok(cCustomerServiceService.unFrozeService(vo));
    }

    /**
     * 解冻客户服务-V2
     */
//    @RequiresPermissions("customer:customerService:unFroze")
    @Log(title = "客户服务", businessType = BusinessType.UNFROZE)
    @PostMapping("/unFrozeServiceV2")
    @ApiOperation(value = "解冻客户服务-V2", notes = "解冻客户服务-V2")
    public Result<CommonOperateDTO> unFrozeServiceV2(@RequestBody CustomerServiceUnFrozeV2VO vo) {
        return Result.ok(cCustomerServiceService.unFrozeServiceV2(vo));
    }

    /**
     * 客户移组
     */
//    @RequiresPermissions("customer:customerService:changeBusinessDept")
    @Log(title = "客户服务", businessType = BusinessType.CHANGE_BUSINESS_DEPT)
    @PostMapping("/changeBusinessDept")
    @ApiOperation(value = "客户移组", notes = "客户移组")
    public Result<CommonOperateDTO> changeBusinessDept(@RequestBody CustomerServiceChangeBusinessDeptVO vo) {
        return Result.ok(cCustomerServiceService.changeBusinessDept(vo));
    }

    /**
     * 客户换区
     */
//    @RequiresPermissions("customer:customerService:changeAccountingTopDept")
    @Log(title = "客户服务", businessType = BusinessType.CHANGE_ACCOUNTING_TOP_DEPT)
    @PostMapping("/changeAccountingTopDept")
    @ApiOperation(value = "客户换区", notes = "客户换区")
    public Result<CommonOperateDTO> changeAccountingTopDept(@RequestBody CustomerServiceChangeAccountingDeptVO vo) {
        return Result.ok(cCustomerServiceService.changeAccountingTopDept(vo));
    }

    /**
     * 分派顾问
     */
//    @RequiresPermissions("customer:customerService:dispatchAdvisor")
    @Log(title = "客户服务", businessType = BusinessType.DISPATCH_ADVISOR)
    @PostMapping("/dispatchAdvisor")
    @ApiOperation(value = "分派顾问", notes = "分派顾问")
    public Result<CommonOperateDTO> dispatchAdvisor(@RequestBody CustomerServiceDispatchVO vo) {
        return Result.ok(cCustomerServiceService.dispatchAdvisor(vo));
    }

    /**
     * 编辑服务标签
     */
//    @RequiresPermissions("customer:customerService:editTag")
    @PostMapping("/editCustomerServiceTag")
    @ApiOperation(value = "编辑服务标签", notes = "编辑服务标签")
    public Result editCustomerServiceTag(@RequestBody CustomerServiceTagVO vo) {
        cCustomerServiceService.editCustomerServiceTag(vo);
        return Result.ok();
    }

    /**
     * 分派会计
     */
//    @RequiresPermissions("customer:customerService:dispatchAccounting")
    @Log(title = "客户服务", businessType = BusinessType.DISPATCH_ACCOUNTING)
    @PostMapping("/dispatchAccounting")
    @ApiOperation(value = "分派会计", notes = "分派会计")
    public Result<CommonOperateDTO> dispatchAccounting(@RequestBody CustomerServiceDispatchVO vo) {
        return Result.ok(cCustomerServiceService.dispatchAccounting(vo));
    }

    @PostMapping("/getDispatchPeriodRange")
    @ApiOperation("分派会计时获取可选的生效账期范围,传ids")
    public Result<CustomerServiceDispatchPeriodRangeDTO> getDispatchPeriodRange(@RequestBody CommonIdVO vo) {
        return Result.ok(cCustomerServiceService.getDispatchPeriodRange(vo.getIds()));
    }

    @PostMapping("/dispatchAccountingV2")
    @ApiOperation(value = "分派会计V2", notes = "分派会计V2")
    public Result<CommonOperateDTO> dispatchAccountingV2(@RequestBody CustomerServiceDispatchV2VO vo) {
        return Result.ok(cCustomerServiceService.dispatchAccountingV2(vo));
    }

    //    @RequiresPermissions("customer:customerService:query")
    @GetMapping(value = "/sysAccount/{customerServiceId}")
    @ApiOperation(value = "获取客户服务系统账号列表", notes = "获取客户服务系统账号列表")
    public Result<List<CustomerServiceSysAccountDTO>> sysAccountList(@PathVariable("customerServiceId") Long customerServiceId) {
        return Result.ok(cCustomerServiceService.sysAccountList(customerServiceId));
    }

    //    @RequiresPermissions("customer:customerServiceSysAccount:add")
    @PostMapping(value = "/sysAccount/add")
    @ApiOperation(value = "创建客户服务系统账号", notes = "创建客户服务系统账号")
    public Result addSysAccount(@RequestBody CustomerServiceSysAccountDTO dto) {
        cCustomerServiceService.addSysAccount(dto);
        return Result.ok();
    }

    @PostMapping(value = "/sysAccount/remoteAdd")
    @ApiIgnore
    @InnerAuth
    public Result remoteAddSysAccount(@RequestBody RemoteCustomerServiceSysAccountVO vo) {
        cCustomerServiceService.remoteAddSysAccount(vo);
        return Result.ok();
    }

    //    @RequiresPermissions("customer:customerServiceSysAccount:update")
    @PostMapping(value = "/sysAccount/update")
    @ApiOperation(value = "编辑客户服务系统账号", notes = "编辑客户服务系统账号")
    public Result updateSysAccount(@RequestBody CustomerServiceSysAccountDTO dto) {
        cCustomerServiceService.updateSysAccount(dto);
        return Result.ok();
    }

    //    @RequiresPermissions("customer:customerServiceSysAccount:delete")
    @DeleteMapping(value = "/sysAccount/delete/{ids}")
    @ApiOperation(value = "删除客户服务系统账号", notes = "删除客户服务系统账号")
    public Result deleteSysAccount(@PathVariable Long[] ids) {
        cCustomerServiceService.deleteSysAccount(ids);
        return Result.ok();
    }

    //    @RequiresPermissions("customer:customerService:query")
    @GetMapping(value = "/taxTypeCheck/{customerServiceId}")
    @ApiOperation(value = "获取客户服务税种核定列表", notes = "获取客户服务税种核定列表")
    public Result<List<CustomerServiceTaxTypeCheckVO>> customerServiceTaxTypeCheckList(@PathVariable("customerServiceId") Long customerServiceId) {
        return Result.ok(cCustomerServiceService.customerServiceTaxTypeCheckList(customerServiceId));
    }

    @GetMapping("/customerServiceFinanceTaxInfo")
    @ApiOperation("7月-获取服务税务信息")
    public Result<CustomerServiceFinanceTaxInfoDTO> customerServiceFinanceTaxInfo(@RequestParam("customerServiceId") Long customerServiceId) {
        return Result.ok(cCustomerServiceService.customerServiceFinanceTaxInfo(customerServiceId));
    }

    //    @RequiresPermissions("customer:customerServiceTaxTypeCheck:edit")
//    @GetMapping("/checkTaxTypeExists")
//    @ApiOperation(value = "判断税种在系统中是否存在，false-不存在，true-已存在", notes = "判断税种在系统中是否存在，false-不存在，true-已存在")
//    public Result<Boolean> checkTaxTypeExists(@RequestParam("taxType") String taxType) {
//        return Result.ok(cCustomerServiceService.checkTaxTypeExists(taxType));
//    }

    //    @RequiresPermissions("customer:customerServiceTaxTypeCheck:edit")
    @PostMapping("/editCustomerServiceTaxTypeCheck")
    @ApiOperation(value = "7月-编辑税种核定（新增了税务信息相关字段）", notes = "编辑税种核定")
    public Result editCustomerServiceTaxTypeCheck(@RequestBody CustomerServiceTaxTypeCheckDTO dto) {
        cCustomerServiceService.editCustomerServiceTaxTypeCheck(dto);
        return Result.ok();
    }

    @GetMapping(value = "/periodTaxTypeCheck/{customerServicePeriodMonthId}")
    @ApiOperation(value = "获取服务账期税种核定列表", notes = "获取服务账期税种核定列表")
    public Result<List<CustomerServiceTaxTypeCheckVO>> customerServicePeriodTaxTypeCheckList(@PathVariable("customerServicePeriodMonthId") Long customerServicePeriodMonthId) {
        return Result.ok(cCustomerServiceService.customerServicePeriodTaxTypeCheckList(customerServicePeriodMonthId));
    }

    @PostMapping("/editCustomerServicePeriodTaxTypeCheck")
    @ApiOperation(value = "编辑账期税种核定", notes = "编辑账期税种核定")
//    @RequiresPermissions("customer:customerServicePeriodTaxTypeCheck:edit")
    public Result editCustomerServicePeriodTaxTypeCheck(@RequestBody CustomerServicePeriodTaxTypeCheckDTO dto) {
        cCustomerServiceService.editCustomerServicePeriodTaxTypeCheck(dto);
        return Result.ok();
    }

    //    @RequiresPermissions("customer:customerService:logQuery")
    @GetMapping("/customerServiceLogList")
    @ApiOperation("客户服务变更记录")
    public Result<IPage<BusinessLogDTO>> customerServiceLogList(@RequestParam("customerServiceId") Long customerServiceId,
                                                                @RequestParam("pageNum") Integer pageNum,
                                                                @RequestParam("pageSize") Integer pageSize) {
        return Result.ok(remoteLogService.getByBusinessIdAndBusinessType(BusinessLogBusinessType.CUSTOMER_SERVICE.getCode(), customerServiceId, pageNum, pageSize).getDataThrowException());
    }

    @PostMapping("/customerServiceChangeNameForce")
    @ApiOperation("强制更名")
    @Log(title = "客户服务", businessType = BusinessType.UPDATE)
//    @RequiresPermissions("customer:customerService:changeName")
    public Result customerServiceChangeNameForce(@RequestBody CustomerServiceChangeNameVO vo) {
        cCustomerServiceService.customerServiceChangeNameForce(vo);
        return Result.ok();
    }

    @GetMapping("/customerServiceWaitItemList")
    @ApiOperation("客户服务待处理列表")
//    @RequiresPermissions(value = {"customer:customerService:waitDispatchList", "customer:customerService:waitReDispatchList", "customer:customerService:waitChangeNameList", "customer:customerService:waitAdvisorDispatchList"}, logical = Logical.OR)
    public Result<IPage<CustomerServiceWaitItemDTO>> customerServiceWaitItemList(@RequestParam("itemType") @ApiParam("待处理类型，1-待派工，2-待重派，3-更名待确认，4-顾问待分派") Integer itemType,
                                                                                 @RequestParam(value = "jumpType", required = false) @ApiParam("跳转类型，1-顾问跳转，2-会计跳转") Integer jumpType,
                                                                                 @RequestParam(value = "keyWord", required = false) @ApiParam("关键字搜索") String keyWord,
                                                                                 @RequestHeader("deptId") Long deptId,
                                                                                 @RequestParam(value = "deptId", required = false) Long queryDeptId,
                                                                                 @RequestParam(value = "deptIds", required = false) String deptIds,
                                                                                 @RequestParam(value = "tagName", required = false) @ApiParam("标签名称") String tagName,
                                                                                 @RequestParam(value = "tagIncludeFlag", required = false) @ApiParam("是否包含标签，0-否，1-是") Integer tagIncludeFlag,
                                                                                 @RequestParam(value = "advisorDeptEmployeeName", required = false) @ApiParam("顾问部门或员工名") String advisorDeptEmployeeName,
                                                                                 @RequestParam(value = "accountingDeptEmployeeName", required = false) @ApiParam("会计部门或员工名") String accountingDeptEmployeeName,
                                                                                 @RequestParam(value = "taxType", required = false) @ApiParam("纳税人性质，1-小规模，2-一般纳税人") Integer taxType,
                                                                                 @RequestParam("pageNum") Integer pageNum,
                                                                                 @RequestParam("pageSize") Integer pageSize) {
        return Result.ok(cCustomerServiceService.customerServiceWaitItemList(itemType, jumpType, keyWord, deptId, queryDeptId, deptIds, tagName, tagIncludeFlag, advisorDeptEmployeeName, accountingDeptEmployeeName, taxType, pageNum, pageSize));
    }

    @GetMapping("/customerServiceWarningList")
    @ApiOperation("客户服务超额预警列表，权限字符：customer:customerService:customerServiceWarningList")
//    @RequiresPermissions(value = "customer:customerService:customerServiceWarningList")
    public Result<IPage<CustomerServiceIncomeExcessDTO>> customerServiceWarningList(@RequestParam(value = "keyWord", required = false) @ApiParam("关键字搜索") String keyWord,
                                                                                    @RequestParam(value = "deptType") @ApiParam("部门类型，1-顾问，2-会计") Integer deptType,
                                                                                    @RequestHeader("deptId") Long deptId,
                                                                                    @RequestParam(value = "deptId", required = false) Long queryDeptId,
                                                                                    @RequestParam(value = "deptIds", required = false) String deptIds,
                                                                                    @RequestParam(value = "tagName", required = false) @ApiParam("标签名称") String tagName,
                                                                                    @RequestParam(value = "tagIncludeFlag", required = false) @ApiParam("是否包含标签，0-否，1-是") Integer tagIncludeFlag,
                                                                                    @RequestParam(value = "advisorDeptEmployeeName", required = false) @ApiParam("顾问部门或员工名") String advisorDeptEmployeeName,
                                                                                    @RequestParam(value = "accountingDeptEmployeeName", required = false) @ApiParam("会计部门或员工名") String accountingDeptEmployeeName,
                                                                                    @RequestParam("pageNum") Integer pageNum,
                                                                                    @RequestParam("pageSize") Integer pageSize) {
        List<Long> advisorSearchDeptIds = Lists.newArrayList();
        if (!StringUtils.isEmpty(advisorDeptEmployeeName)) {
            advisorSearchDeptIds = remoteDeptService.selectDeptIdsByDeptEmployeeName(advisorDeptEmployeeName).getDataThrowException();
            if (ObjectUtils.isEmpty(advisorSearchDeptIds)) {
                return Result.ok(new Page<>(pageNum, pageSize));
            }
        }
        List<Long> accountingSearchDeptIds = Lists.newArrayList();
        if (!StringUtils.isEmpty(accountingDeptEmployeeName)) {
            accountingSearchDeptIds = remoteDeptService.selectDeptIdsByDeptEmployeeName(accountingDeptEmployeeName).getDataThrowException();
            if (ObjectUtils.isEmpty(accountingSearchDeptIds)) {
                return Result.ok(new Page<>(pageNum, pageSize));
            }
        }
        List<Long> queryDeptIds = commonService.getAllChildrenDeptIdsByDeptIds(queryDeptId, deptIds);
        return Result.ok(cCustomerServiceService.customerServiceWarningList(keyWord, deptId, queryDeptIds, deptType, tagName, tagIncludeFlag, advisorSearchDeptIds, accountingSearchDeptIds, pageNum, pageSize));
    }

    @GetMapping("/thisMonthEndList")
    @ApiOperation("本月到期客户列表，权限字符：customer:customerService:thisMonthEndList")
//    @RequiresPermissions(value = "customer:customerService:thisMonthEndList")
    public Result<IPage<CustomerServiceDTO>> thisMonthEndList(@RequestParam(value = "keyWord", required = false) @ApiParam("关键字搜索") String keyWord,
                                                              @RequestParam(value = "deptType") @ApiParam("部门类型，1-顾问，2-会计") Integer deptType,
                                                              @RequestHeader("deptId") Long deptId,
                                                              @RequestParam(value = "deptId", required = false) Long queryDeptId,
                                                              @RequestParam(value = "tagName", required = false) @ApiParam("标签名称") String tagName,
                                                              @RequestParam(value = "tagIncludeFlag", required = false) @ApiParam("是否包含标签，0-否，1-是") Integer tagIncludeFlag,
                                                              @RequestParam(value = "advisorDeptEmployeeName", required = false) @ApiParam("顾问部门或员工名") String advisorDeptEmployeeName,
                                                              @RequestParam(value = "accountingDeptEmployeeName", required = false) @ApiParam("会计部门或员工名") String accountingDeptEmployeeName, @RequestParam("pageNum") Integer pageNum,
                                                              @RequestParam("pageSize") Integer pageSize) {
        return Result.ok(cCustomerServiceService.thisMonthEndList(keyWord, deptId, queryDeptId, tagName, tagIncludeFlag, deptType, advisorDeptEmployeeName, accountingDeptEmployeeName, pageNum, pageSize));
    }

    @PostMapping("/thisMonthEndListExport")
    @ApiOperation("本月到期客户列表，权限字符：customer:customerService:thisMonthEndListExport")
//    @RequiresPermissions(value = "customer:customerService:thisMonthEndListExport")
    public void thisMonthEndListExport(@RequestParam(value = "keyWord", required = false) @ApiParam("关键字搜索") String keyWord,
                                       @RequestParam(value = "deptType") @ApiParam("部门类型，1-顾问，2-会计") Integer deptType,
                                       @RequestHeader("deptId") Long deptId,
                                       @RequestParam(value = "deptId", required = false) Long queryDeptId,
                                       @RequestParam(value = "tagName", required = false) @ApiParam("标签名称") String tagName,
                                       @RequestParam(value = "tagIncludeFlag", required = false) @ApiParam("是否包含标签，0-否，1-是") Integer tagIncludeFlag,
                                       @RequestParam(value = "advisorDeptEmployeeName", required = false) @ApiParam("顾问部门或员工名") String advisorDeptEmployeeName,
                                       @RequestParam(value = "accountingDeptEmployeeName", required = false) @ApiParam("会计部门或员工名") String accountingDeptEmployeeName,
                                       HttpServletResponse response) {
        List<CustomerServiceDTO> records = cCustomerServiceService.thisMonthEndList(keyWord, deptId, queryDeptId, tagName, tagIncludeFlag, deptType, advisorDeptEmployeeName, accountingDeptEmployeeName, 1, 5000).getRecords();
        ExcelUtil<CustomerServiceDTO> util = new ExcelUtil<>(CustomerServiceDTO.class);
        util.exportExcel(response, records, "本月到期客户数据");
    }

    /**
     * 导出客户服务超额预警列表
     */
//    @RequiresPermissions("customer:customerService:warningListExport")
    @PostMapping("/warningListExport")
    @ApiOperation(value = "导出客户服务超额预警列表，权限字符：customer:customerService:warningListExport", notes = "导出客户服务超额预警列表")
    public void warningListExport(HttpServletResponse response, @RequestHeader("deptId") Long deptId, CustomerServiceWarningSearchVO vo) {
        vo.setPageNum(1);
        vo.setPageSize(5000);
        List<Long> advisorSearchDeptIds = Lists.newArrayList();
        if (!StringUtils.isEmpty(vo.getAdvisorDeptEmployeeName())) {
            advisorSearchDeptIds = remoteDeptService.selectDeptIdsByDeptEmployeeName(vo.getAdvisorDeptEmployeeName()).getDataThrowException();
            if (ObjectUtils.isEmpty(advisorSearchDeptIds)) {
                List<CustomerServiceIncomeExcessDTO> list = Lists.newArrayList();
                ExcelUtil<CustomerServiceIncomeExcessDTO> util = new ExcelUtil<>(CustomerServiceIncomeExcessDTO.class);
                util.exportExcel(response, list, "开票超额预警数据");
            }
        }

        List<Long> accountingSearchDeptIds = Lists.newArrayList();
        if (!StringUtils.isEmpty(vo.getAccountingDeptEmployeeName())) {
            accountingSearchDeptIds = remoteDeptService.selectDeptIdsByDeptEmployeeName(vo.getAccountingDeptEmployeeName()).getDataThrowException();
            if (ObjectUtils.isEmpty(accountingSearchDeptIds)) {
                List<CustomerServiceIncomeExcessDTO> list = Lists.newArrayList();
                ExcelUtil<CustomerServiceIncomeExcessDTO> util = new ExcelUtil<>(CustomerServiceIncomeExcessDTO.class);
                util.exportExcel(response, list, "开票超额预警数据");
            }
        }

        List<Long> queryDeptIds = commonService.getAllChildrenDeptIdsByDeptIds(vo.getDeptId(), vo.getDeptIds());
        List<CustomerServiceIncomeExcessDTO> list = cCustomerServiceService.customerServiceWarningList(vo.getCustomerName(), deptId, queryDeptIds, vo.getDeptType(), vo.getTagName(), vo.getTagIncludeFlag(), advisorSearchDeptIds, accountingSearchDeptIds, vo.getPageNum(), vo.getPageSize()).getRecords();
        ExcelUtil<CustomerServiceIncomeExcessDTO> util = new ExcelUtil<>(CustomerServiceIncomeExcessDTO.class);
        util.exportExcel(response, list, "开票超额预警数据");
    }

    @PostMapping("/notDispatch")
    @ApiOperation("无需分派")
//    @RequiresPermissions("customer:customerService:notDispatch")
    @Log(title = "客户服务", businessType = BusinessType.NOT_DISPATCH)
    public Result notDispatchAccounting(@RequestBody CommonIdVO vo, @RequestHeader("deptId") Long deptId) {
        cCustomerServiceService.notDispatch(vo, deptId);
        return Result.ok();
    }

    @PostMapping("/confirmChangeName")
    @ApiOperation("确认更名")
//    @RequiresPermissions("customer:customerService:confirmChangeName")
    public Result<CommonOperateDTO> confirmChangeName(@RequestBody CommonIdVO vo) {
        return Result.ok(cCustomerServiceService.confirmChangeName(vo));
    }

    @GetMapping("/customerServiceIncome")
    @ApiOperation("客户服务收入汇总")
//    @RequiresPermissions("customer:customerService:query")
    public Result<CustomerServiceIncomeDTO> customerServiceIncome(@RequestParam("customerServiceId") Long customerServiceId) {
        return Result.ok(cCustomerServiceService.customerServiceIncome(customerServiceId));
    }

    /**
     * 客户管理 -> 客户详情 -> 年度汇总 -> 客户服务的年账期开始
     * mmnAPI
     */
    @GetMapping("/customerServiceStartYear")
    @ApiOperation("客户管理 -> 客户详情 -> 年度汇总 -> 客户服务的年账期开始")
//    @RequiresPermissions("customer:customerService:query")
    public Result<CustomerServiceStartYearDTO> customerServiceStartYear(
            @RequestParam("customerServiceId") Long customerServiceId
    ) {
        return Result.ok(cCustomerServiceService.customerServiceStartYear(customerServiceId));
    }

    /**
     * 客户管理 -> 客户详情 -> 年度汇总
     * mmnAPI
     */
    @GetMapping("/customerServiceYearCollect")
    @ApiOperation("客户管理 -> 客户详情 -> 年度汇总")
//    @RequiresPermissions("customer:customerService:query")
    public Result<CustomerServiceYearCollectDTO> customerServiceYearCollect(
            @RequestParam("customerServiceId") Long customerServiceId,
            @RequestParam("year") Integer year
    ) {
        return Result.ok(cCustomerServiceService.customerServiceYearCollect(customerServiceId, year));
    }

    @GetMapping("/customerServiceYearCollectV2")
    @ApiOperation("客户管理 -> 客户详情 -> 年度汇总")
//    @RequiresPermissions("customer:customerService:query")
    public Result<CustomerServiceYearCollectV2DTO> customerServiceYearCollectV2(
            @RequestParam("customerServiceId") Long customerServiceId,
            @RequestParam("year") Integer year
    ) {
        return Result.ok(cCustomerServiceService.customerServiceYearCollectV2(customerServiceId, year));
    }

    /**
     * 客户管理 -> 客户详情 -> 账期明细 -> 月度账期列表
     * mmnAPI
     */
    @GetMapping("/customerServiceMonthPeriodList")
    @ApiOperation("客户管理 -> 客户详情 -> 账期明细 -> 月度账期列表")
//    @RequiresPermissions("customer:customerService:query")
    public Result<List<CustomerServiceMonthPeriodDTO>> customerServiceMonthPeriodList(
            @RequestParam("customerServiceId") Long customerServiceId,
            @RequestParam("year") Integer year
    ) {
        return Result.ok(cCustomerServiceService.customerServiceMonthPeriodList(customerServiceId, year));
    }

    @GetMapping("/customerServiceMonthPeriodListV2")
    @ApiOperation("客户管理 -> 客户详情 -> 账期明细 -> 月度账期列表")
//    @RequiresPermissions("customer:customerService:query")
    public Result<List<CustomerServiceMonthPeriodV2DTO>> customerServiceMonthPeriodListV2(
            @RequestParam("customerServiceId") Long customerServiceId,
            @RequestParam("year") Integer year
    ) {
        return Result.ok(cCustomerServiceService.customerServiceMonthPeriodListV2(customerServiceId, year));
    }

    /**
     * 客户管理 -> 账期详情 -> 获取账期详情数据
     * mmnAPI
     */
    @GetMapping("/customerServiceMonthPeriodDetail")
    @ApiOperation("客户管理 -> 账期详情 -> 获取账期详情数据")
//    @RequiresPermissions("customer:customerService:query")
    public Result<CustomerServiceMonthPeriodDetailDTO> customerServiceMonthPeriodDetail(
            @RequestParam("customerServiceId") Long customerServiceId,
            @RequestParam("customerServicePeriodMonthId") Long customerServicePeriodMonthId,
            @RequestHeader("deptId") Long deptId
    ) {
        return Result.ok(cCustomerServiceService.customerServiceMonthPeriodDetail(customerServiceId, customerServicePeriodMonthId, deptId));
    }

    @GetMapping("/getCompanyInfo")
    @ApiOperation("获取工商信息")
    public Result<RemoteCompanyInfoDTO> getCompanyInfo(@RequestParam("customerServiceId") Long customerServiceId) {
        return Result.ok(cCustomerServiceService.getCompanyInfo(customerServiceId));
    }

    @PostMapping("/updateBusinessInformation")
    @ApiOperation("更新工商信息")
//    @RequiresPermissions("customer:customerService:updateBusinessInformation")
    @Log(title = "客户服务", businessType = BusinessType.UPDATE_BUSINESS_INFORMATION)
    public Result updateBusinessInformation(@RequestBody CommonIdVO vo) {
        cCustomerServiceService.updateBusinessInformation(vo.getId());
        return Result.ok();
    }

    @GetMapping("/getCustomerServiceDeptCapacityInfo")
    @ApiOperation("获取会计组别容量信息")
    public Result<CustomerServiceDeptCapacityDTO> getCustomerServiceDeptCapacityInfo(@RequestParam("deptId") Long deptId) {
        return Result.ok(cCustomerServiceService.getCustomerServiceDeptCapacityInfo(deptId));
    }

    @GetMapping("/checkHasCustomerService")
    @ApiOperation("内部接口，前端无需调用")
    public Result<Boolean> checkHasCustomerService(@RequestParam("deptId") Long deptId) {
        return Result.ok(cCustomerServiceService.checkHasCustomerService(deptId));
    }

    @GetMapping("/tagListSelect")
    @ApiOperation("标签池数据")
    public Result<List<TagDTO>> tagListSelect() {
        return Result.ok(cCustomerServiceService.tagListSelect());
    }

    @GetMapping("/getCompanyInfoByKeyWord")
    @ApiOperation("企查查-根据企业名称模糊查询企业信息")
    public Result<List<RemoteCompanyInfoDTO>> getCompanyInfoByKeyWord(@RequestParam("keyWord") String keyWord) {
        return Result.ok(cCustomerServiceService.getCompanyInfoByKeyWord(keyWord));
    }

    @GetMapping("/customerSearch")
    @ApiOperation("小工具-客户查询")
//    @RequiresPermissions("customer:customerService:customerSearch")
    public Result<CustomerSearchResultDTO> customerSearch(@RequestHeader("deptId") Long deptId,
                                                          @RequestParam("keyWord") @ApiParam("查询关键字") String keyWord) {
        return Result.ok(cCustomerServiceService.customerSearch(deptId, keyWord));
    }

    @PostMapping("/getCreditCodeByListCreditCode")
    @ApiOperation("内部接口")
    public Result<List<CCustomerService>> getCreditCodeByListCreditCode(@RequestBody List<String> creditCodes) {
        return Result.ok(cCustomerServiceService.getCreditCodeByListCreditCode(creditCodes));
    }

    @PostMapping("/getByTaxNumberList")
    @ApiIgnore
    public Result<List<CCustomerService>> getByTaxNumberList(@RequestBody List<String> taxNumberList) {
        return Result.ok(cCustomerServiceService.getByTaxNumberList(taxNumberList));
    }

    @PostMapping("/getCustomerPeriodByCreditCodeAndPeriod")
    @ApiOperation("内部接口")
    public Result<List<CustomerServicePeriodMonth>> getCustomerPeriodByCreditCodeAndPeriod(@RequestBody CustomerPeriodVO vo) {
        return Result.ok(customerServicePeriodMonthService.getCustomerPeriodByCreditCodeAndPeriod(vo));
    }

    @PostMapping("/getCustomerPeriodByListCreditCodeAndPeriod")
    @ApiOperation("内部接口")
    public Result<List<CustomerServicePeriodMonth>> getCustomerPeriodByListCreditCodeAndPeriod(@RequestBody List<RemoteCreditCodePeriodVO> voList) {
        return Result.ok(cCustomerServiceService.getCustomerPeriodByListCreditCodeAndPeriod(voList));
    }

    @GetMapping("/getCustomerServiceBankAccountList")
    @ApiOperation("7月-获取客户服务银行卡列表")
    public Result<List<CustomerServiceBankAccountDTO>> getCustomerServiceBankAccountList(@RequestParam("id") @ApiParam("客户服务id") Long id) {
        return Result.ok(cCustomerServiceService.getCustomerServiceBankAccountList(id));
    }

    @PostMapping("/deleteCustomerServiceBankAccount")
    @ApiOperation("7月-删除客户服务银行卡信息，单个删除传id")
    public Result deleteCustomerServiceBankAccount(@RequestBody CommonIdVO vo, @RequestHeader("deptId") Long deptId) {
        cCustomerServiceService.deleteCustomerServiceBankAccount(vo, deptId);
        return Result.ok();
    }

    @PostMapping("/addCustomerServiceBankAccount")
    @ApiOperation("7月-新增客户服务银行卡信息")
    public Result addCustomerServiceBankAccount(@RequestBody CustomerServiceBankAccountDTO vo, @RequestHeader("deptId") Long deptId) {
        cCustomerServiceService.addCustomerServiceBankAccount(vo, deptId);
        return Result.ok();
    }

    @PostMapping("/modifyCustomerServiceBankAccount")
    @ApiOperation("7月-编辑客户服务银行卡信息")
    public Result modifyCustomerServiceBankAccount(@RequestBody CustomerServiceBankAccountDTO vo, @RequestHeader("deptId") Long deptId) {
        cCustomerServiceService.modifyCustomerServiceBankAccount(vo, deptId);
        return Result.ok();
    }

    @GetMapping("/getCustomerPeriodByPeriodRange")
    @ApiOperation("内部接口")
    public Result<List<RemoteCustomerPeriodDTO>> getCustomerPeriodByPeriodRange(@RequestParam("periodMin") Integer periodMin,
                                                                                @RequestParam("periodMax") Integer periodMax,
                                                                                @RequestParam("deptId") Long deptId,
                                                                                @RequestParam("userId") Long userId) {
        return Result.ok(customerServicePeriodMonthService.getCustomerPeriodByPeriodRange(periodMin, periodMax, deptId, userId));
    }

    @GetMapping("/customerServicePeriodYearList")
    @ApiOperation("客户服务年度列表")
//    @RequiresPermissions("customer:customerService:periodYearList")
    public Result<IPage<CustomerServicePeriodYearDTO>> customerServicePeriodYearList(@RequestHeader("deptId") Long deptId,
                                                                                     CustomerServicePeriodYearSearchVO vo) {
        return Result.ok(cCustomerServiceService.customerServicePeriodYearList(deptId, vo));
    }

    @PostMapping("/customerServicePeriodYearExport")
    @ApiOperation("客户服务年度列表导出")
//    @RequiresPermissions("customer:customerService:periodYearExport")
    public void customerServicePeriodYearExport(@RequestHeader("deptId") Long deptId,
                                                CustomerServicePeriodYearSearchVO vo,
                                                HttpServletResponse httpServletResponse) {
        ExcelUtil<CustomerServicePeriodYearDTO> util = new ExcelUtil<>(CustomerServicePeriodYearDTO.class);
        util.exportExcel(httpServletResponse, cCustomerServiceService.selectCustomerServicePeriodYearList(deptId, vo), "客户服务年度列表");
    }

    @PostMapping("/customerServicePeriodYearExportAndUpload")
    @ApiOperation("客户服务年度列表导出(上传oss)")
//    @RequiresPermissions("customer:customerService:periodYearExport")
    public Result customerServicePeriodYearExportAndUpload(@RequestHeader("deptId") Long deptId,
                                                CustomerServicePeriodYearSearchVO vo) {
        String title = "服务-年度汇总" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        vo.setDeptId(deptId);
        Long downloadRecordId = downloadRecordService.createRecord(title, 0L, 0L, vo, DownloadType.SERVICE_ANNUAL_SUMMARY);
        CompletableFuture.runAsync(() -> {
            try {
                List<CustomerServicePeriodYearDTO> list = Lists.newArrayList();
                Integer pageNum = 1;
                Integer pageSize = 5000;
                vo.setPageSize(pageSize);
                while (true) {
                    vo.setPageNum(pageNum);
                    List<CustomerServicePeriodYearDTO> l = cCustomerServiceService.customerServicePeriodYearList(deptId, vo).getRecords();
                    if (!ObjectUtils.isEmpty(l)) {
                        list.addAll(l);
                        pageNum++;
                    } else {
                        break;
                    }
                }
                downloadRecordService.updateDataCount(downloadRecordId, (long) list.size());
                ExcelUtil<CustomerServicePeriodYearDTO> util = new ExcelUtil<>(CustomerServicePeriodYearDTO.class);
                asyncService.uploadExport(util, list, title, downloadRecordId);
            } catch (Exception e) {
                downloadRecordService.updateDownloadError(downloadRecordId, e.getMessage());
            }
        });
        return Result.ok();
    }

    @PostMapping("/modifyCustomerServicePeriodYear")
    @ApiOperation("编辑年度账期")
    public Result modifyCustomerServicePeriodYear(@RequestBody CustomerServicePeriodYearVO vo) {
        customerServicePeriodYearService.modifyCustomerServicePeriodYear(vo);
        return Result.ok();
    }

    @PostMapping("/modifyAccountingRemark")
    @ApiOperation("修改会计备注，权限字符：customer:customerService:modifyAccountingRemark")
//    @RequiresPermissions("customer:customerService:modifyAccountingRemark")
    public Result modifyAccountingRemark(@RequestHeader("deptId") Long deptId,
                                         @RequestBody ModifyRemarkVO vo) {
        cCustomerServiceService.modifyAccountingRemark(deptId, vo);
        return Result.ok();
    }

    @PostMapping("/modifyAdvisorRemark")
    @ApiOperation("修改顾问备注，权限字符：customer:customerService:modifyAdvisorRemark")
//    @RequiresPermissions("customer:customerService:modifyAdvisorRemark")
    public Result modifyAdvisorRemark(@RequestHeader("deptId") Long deptId,
                                      @RequestBody ModifyRemarkVO vo) {
        cCustomerServiceService.modifyAdvisorRemark(deptId, vo);
        return Result.ok();
    }

    @GetMapping("/getCustomerServiceRecentlyAccountingDeptInfo")
    @ApiOperation("获取客户最近一个账期的会计小组信息")
    public Result<DispatchDeptDTO> getCustomerServiceRecentlyAccountingDeptInfo(@RequestParam("customerServiceId") @ApiParam("客户服务id") Long customerServiceId) {
        return Result.ok(cCustomerServiceService.getCustomerServiceRecentlyAccountingDeptInfo(customerServiceId));
    }

    @PostMapping("/getCustomerServicePeriodYearByCustomerServiceIdsAndPeriod")
    @ApiIgnore
    @InnerAuth
    public Result<List<CustomerServicePeriodYear>> getCustomerServicePeriodYearByCustomerServiceIdsAndPeriod(@RequestBody RemoteCustomerServicePeriodYearSearchVO vo) {
        return Result.ok(customerServicePeriodYearService.getCustomerServicePeriodYearByCustomerServiceIdsAndPeriod(vo));
    }

    @PostMapping("/remoteUpdateCustomerServicePeriodYear")
    @ApiIgnore
    @InnerAuth
    public Result remoteUpdateCustomerServicePeriodYear(@RequestBody RemoteCustomerServicePeriodYearVO vo) {
        customerServicePeriodYearService.remoteUpdateCustomerServicePeriodYear(vo);
        return Result.ok();
    }

    @PostMapping("/getCustomerInAccountMaxPeriod")
    @ApiIgnore
    @InnerAuth
    public Result<List<RemoteCustomerInAccountMaxPeriodDTO>> getCustomerInAccountMaxPeriod(@RequestBody List<Long> customerServiceIds) {
        return Result.ok(customerServiceInAccountService.getCustomerInAccountMaxPeriod(customerServiceIds));
    }

    @PostMapping("/getCustomerInAccountGroupRelation")
    @ApiIgnore
    @InnerAuth
    public Result<List<RemoteCustomerInAccountMaxPeriodDTO>> getCustomerInAccountGroupRelation(@RequestBody List<Long> customerServiceIds) {
        return Result.ok(customerServiceInAccountService.getCustomerInAccountGroupRelation(customerServiceIds));
    }

    @PostMapping("/getCustomerBankAccountNumberByBankNumbers")
    @ApiIgnore
    @InnerAuth
    public Result<List<RemoteCustomerBankAccountDTO>> getCustomerBankAccountNumberByBankNumbers(@RequestBody RemoteCustomerBankAccountNumberSearchVO vo) {
        return Result.ok(cCustomerServiceService.getCustomerBankAccountNumberByBankNumbers(vo));
    }

    @GetMapping("/getCustomerBankAccountNumberByBankNumberAndBusinessTopDeptId")
    @ApiIgnore
    @InnerAuth
    public Result<RemoteCustomerBankAccountDTO> getCustomerBankAccountNumberByBankNumberAndBusinessTopDeptId(@RequestParam("bankAccountNumber") String bankAccountNumber,
                                                                                                             @RequestParam("businessTopDeptId") Long businessTopDeptId) {
        return Result.ok(cCustomerServiceService.getCustomerBankAccountNumberByBankNumberAndBusinessTopDeptId(bankAccountNumber, businessTopDeptId));
    }

    @PostMapping("/createCustomerBankAccount")
    @ApiIgnore
    @InnerAuth
    public Result createCustomerBankAccount(@RequestBody RemoteCustomerBankAccountVO vo) {
        cCustomerServiceService.createCustomerBankAccount(vo);
        return Result.ok();
    }

    @PostMapping("/getCustomerPeriodBankList")
    @ApiIgnore
    @InnerAuth
    public Result<List<RemoteCustomerPeriodBankDTO>> getCustomerPeriodBankList(@RequestBody List<String> bankAccountNumbers) {
        return Result.ok(cCustomerServiceService.getCustomerPeriodBankList(bankAccountNumbers));
    }

    @PostMapping("/getCustomerBankListByCustomerServiceIds")
    @ApiIgnore
    @InnerAuth
    public Result<List<RemoteCustomerBankAccountDTO>> getCustomerBankListByCustomerServiceIds(@RequestBody List<Long> customerServiceIds) {
        return Result.ok(cCustomerServiceService.getCustomerBankListByCustomerServiceIds(customerServiceIds));
    }

    @PostMapping("/remoteUpdateCustomerTag")
    @ApiIgnore
    @InnerAuth
    public Result remoteUpdateCustomerTag(@RequestBody RemoteUpdateCustomerTagVO vo) {
        cCustomerServiceService.remoteUpdateCustomerTag(vo);
        return Result.ok();
    }

    @PostMapping("/getCustomerTagsByCustomerServiceIds")
    @ApiIgnore
    @InnerAuth
    public Result<List<RemoteCustomerTagDTO>> getCustomerTagsByCustomerServiceIds(@RequestBody List<Long> customerServiceIds) {
        return Result.ok(cCustomerServiceService.getCustomerTagsByCustomerServiceIds(customerServiceIds));
    }

    @PostMapping("/customerServiceXmList")
    @ApiIgnore
    @InnerAuth
    public Result<PageResult<CustomerServiceXmDTO>> customerServiceXmList(@RequestBody UserCustomerSearchVO vo) {
        return Result.ok(cCustomerServiceService.customerServiceXmList(vo));
    }

    @PostMapping("/userCustomerById")
    @ApiIgnore
    @InnerAuth
    public Result<CustomerServiceXmDTO> userCustomerById(@RequestBody CommonIdVO vo) {
        return Result.ok(cCustomerServiceService.userCustomerById(vo));
    }

    @PostMapping("/changePeriodBusinessDept")
    @ApiOperation(value = "账期移组", notes = "账期移组")
    public Result<TCommonOperateDTO<CustomerServicePeriodMonth>> changePeriodBusinessDept(@RequestHeader("deptId") Long deptId,
                                                                                          @RequestBody CustomerServicePeriodMonthChangeDeptVO vo) {
        return Result.ok(cCustomerServiceService.changePeriodBusinessDept(deptId, vo));
    }

    @PostMapping("/changePeriodAccountingTopDept")
    @ApiOperation(value = "账期换区", notes = "账期换区")
    public Result<TCommonOperateDTO<CustomerServicePeriodMonth>> changePeriodAccountingTopDept(@RequestHeader("deptId") Long deptId,
                                                                                               @RequestBody CustomerServicePeriodMonthChangeDeptVO vo) {
        return Result.ok(cCustomerServiceService.changePeriodAccountingTopDept(deptId, vo));
    }

    @PostMapping("/changePeriodAdvisorDept")
    @ApiOperation(value = "账期分派顾问", notes = "账期分派顾问")
    public Result<TCommonOperateDTO<CustomerServicePeriodMonth>> changePeriodAdvisorDept(@RequestHeader("deptId") Long deptId,
                                                                                         @RequestBody CustomerServicePeriodMonthChangeDeptVO vo) {
        return Result.ok(cCustomerServiceService.changePeriodAdvisorDept(deptId, vo));
    }

    @PostMapping("/changePeriodAccountingDept")
    @ApiOperation(value = "账期分派会计", notes = "账期分派会计")
    public Result<TCommonOperateDTO<CustomerServicePeriodMonth>> changePeriodAccountingDept(@RequestHeader("deptId") Long deptId,
                                                                                            @RequestBody CustomerServicePeriodMonthChangeDeptVO vo) {
        return Result.ok(cCustomerServiceService.changePeriodAccountingDept(deptId, vo));
    }

    @PostMapping("/downloadPeriodOperateErrorRecord/{batchNo}")
    @ApiOperation("统一的账期下载操作异常的记录表格（同步导出）")
    public void downloadPeriodOperateErrorRecord(HttpServletResponse response, @PathVariable("batchNo") String batchNo) {
        List<CustomerServicePeriodMonthOperateErrorDTO> errorDTOList = redisService.getLargeCacheList(CacheConstants.PERIOD_MONTH_OPERATE_ERROR_RECORD + batchNo, 500);
        errorDTOList = ObjectUtils.isEmpty(errorDTOList) ? Lists.newArrayList() : errorDTOList;
        ExcelUtil<CustomerServicePeriodMonthOperateErrorDTO> util = new ExcelUtil<>(CustomerServicePeriodMonthOperateErrorDTO.class);
        util.exportExcel(response, errorDTOList, "账期操作异常记录");
    }

    @PostMapping("/updateTaxType")
    @ApiOperation("纳税人性质升级")
    public Result updateTaxType(@RequestBody UpdateTaxTypeVO vo,
                                @RequestHeader("deptId") Long deptId) {
        cCustomerServiceService.updateTaxType(vo, deptId);
        return Result.ok();
    }
}
