package com.bxm.customer.domain.dto;

import com.bxm.system.api.domain.BusinessLogDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerServiceDetailDTO {

    @ApiModelProperty("客户服务id")
    private Long id;

    @ApiModelProperty("服务状态,1-服务中，2-已结束")
    private Integer serviceStatus;

    @ApiModelProperty("集团id")
    private Long businessTopDeptId;

    @ApiModelProperty("集团名称")
    private String businessTopDeptName;

    @ApiModelProperty("业务公司id")
    @NotNull(message = "请选择业务公司")
    private Long businessDeptId;

    @ApiModelProperty("业务公司名称")
    private String businessDeptName;

    @ApiModelProperty("业务公司id全路径")
    private List<Long> businessDeptIdPath;

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("客户公司名称")
    @NotEmpty(message = "请选择客户企业")
    private String customerCompanyName;

    @ApiModelProperty("统一社会信用代码")
    @NotEmpty(message = "信用代码不能为空")
    private String creditCode;

    @ApiModelProperty("纳税人识别号")
    @NotEmpty(message = "税号不能为空")
    private String taxNumber;

    @ApiModelProperty("服务编号")
    @NotEmpty(message = "服务编号不能为空")
    private String serviceNumber;

    @ApiModelProperty("首个账期，yyyyMM")
    @NotNull(message = "首个账期不能为空")
    private Integer firstAccountPeriod;

    @ApiModelProperty("纳税人性质")
    @NotNull(message = "纳税人性质不能为空")
    private Integer taxType;

    @ApiModelProperty("标签")
    private List<TagDTO> tags;

    @ApiModelProperty("顾问组别id")
    @NotNull(message = "请选择顾问组别")
    private Long advisorDeptId;

    @ApiModelProperty("顾问小组id全路径")
    private List<Long> advisorDeptIdPath;

    @ApiModelProperty("顾问组别名称")
    private String advisorDeptName;

    @ApiModelProperty("会计组别id")
    private Long accountingDeptId;

    @ApiModelProperty("会计组别名称")
    private String accountingDeptName;

    @ApiModelProperty("会计部门id")
    private Long accountingParentDeptId;

    private Long accountingTopDeptId;

    @ApiModelProperty("会计部门名称")
    private String accountingParentDeptName;

    private String accountingTopDeptName;

    @ApiModelProperty("会计部门id全路径")
    private List<Long> accountingDeptIdPath;

    @ApiModelProperty("顾问员工名称")
    private String advisorEmployeeName;

    @ApiModelProperty("会计员工名称")
    private String accountingEmployeeName;

    @ApiModelProperty("开始账期，null表示没有")
    private String startPeriod;

    @ApiModelProperty("结束账期，null表示没有")
    private String endPeriod;

    @ApiModelProperty("信息变更原因")
    private String changeReason;

    @ApiModelProperty("生效账期")
    private Integer validPeriod;

    private BusinessLogDTO businessLogDTO;

    private Long headerDeptId;

    @ApiModelProperty("顾问备注")
    private String advisorRemark;

    @ApiModelProperty("是否可以修改顾问备注")
    private Boolean canModifyAdvisorRemark;

    @ApiModelProperty("会计备注")
    private String accountingRemark;

    @ApiModelProperty("是否可以修改会计备注")
    private Boolean canModifyAccountingRemark;
}
